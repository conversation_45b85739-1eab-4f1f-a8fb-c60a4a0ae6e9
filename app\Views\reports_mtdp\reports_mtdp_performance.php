<?php
// app/Views/reports_mtdp/reports_mtdp_performance.php
/**
 * MTDP Performance Analytics Dashboard
 */
?>
<?= $this->extend('templates/system_template') ?>

<?= $this->section('head') ?>
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">
<style>
.performance-card {
    transition: transform 0.2s;
}
.performance-card:hover {
    transform: translateY(-5px);
}
.metric-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.activity-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.financial-card {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
.strategic-card {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

/* Fix text visibility issues */
.card-body {
    color: #2c3e50 !important;
}
.card-body h1, .card-body h2, .card-body h3, .card-body h4, .card-body h5, .card-body h6 {
    color: #2c3e50 !important;
    font-weight: 600;
}
.card-body p, .card-body span, .card-body div, .card-body small {
    color: #2c3e50 !important;
}
.text-muted {
    color: #6c757d !important;
}
.form-label {
    color: #2c3e50 !important;
    font-weight: 600;
}
.btn {
    font-weight: 500;
}
/* Ensure table text is visible */
.table {
    color: #2c3e50 !important;
}
.table th, .table td {
    color: #2c3e50 !important;
}
/* Chart container text */
.chart-container {
    color: #2c3e50 !important;
}
/* Modal text */
.modal-body {
    color: #2c3e50 !important;
}
/* Navigation links */
.nav-link {
    color: #2c3e50 !important;
    font-weight: 500;
}
.nav-link.active {
    background-color: #6ba84f !important;
    color: white !important;
}
.nav-link:hover {
    color: #6ba84f !important;
}
/* Ensure all text in cards is visible */
.card .text-muted {
    color: #6c757d !important;
}
/* Fix any remaining white text issues */
* {
    color: inherit;
}
.text-white * {
    color: white !important;
}
/* Table specific styling */
.table thead th {
    color: white !important;
    background-color: #2c3e50 !important;
}
.table tbody td {
    color: #2c3e50 !important;
    font-weight: 500;
}
.table tbody tr:hover {
    background-color: #f8f9fa !important;
}
/* Badge styling */
.badge {
    font-weight: 600;
}
/* Button styling */
.btn-outline-info, .btn-outline-primary {
    color: #2c3e50 !important;
    border-color: #6ba84f !important;
}
.btn-outline-info:hover, .btn-outline-primary:hover {
    background-color: #6ba84f !important;
    color: white !important;
}
/* Canvas container */
canvas {
    background-color: white;
}
/* Progress bar container */
.progress {
    background-color: #e9ecef !important;
}
/* Small text visibility */
small {
    color: #6c757d !important;
    font-weight: 500;
}
/* Insights section */
.bg-light .card-body {
    color: #2c3e50 !important;
}
.bg-light .card-title {
    color: #2c3e50 !important;
    font-weight: 600;
}
.list-unstyled li {
    color: #2c3e50 !important;
}
.list-unstyled strong {
    color: #2c3e50 !important;
}
/* Modal styling */
.modal-content {
    color: #2c3e50 !important;
}
.modal-header {
    color: white !important;
}
.modal-body {
    color: #2c3e50 !important;
}
/* Loading text */
.text-center {
    color: #2c3e50 !important;
}
/* Icon colors */
.fas {
    opacity: 1;
}
/* Ensure all text elements are visible */
p, span, div, li, td, th, label, input, select, textarea {
    color: inherit !important;
}
/* Override any Bootstrap text utilities that might be causing issues */
.text-dark {
    color: #2c3e50 !important;
}

/* Force ALL text to be dark - comprehensive override */
body, html {
    color: #2c3e50 !important;
}

/* All text elements */
h1, h2, h3, h4, h5, h6, p, span, div, a, li, td, th, label, input, select, textarea, small, strong, em, i, b {
    color: #2c3e50 !important;
}

/* Specific overrides for common Bootstrap classes */
.text-white {
    color: white !important;
}

.text-muted {
    color: #6c757d !important;
}

.text-primary {
    color: #0d6efd !important;
}

.text-success {
    color: #198754 !important;
}

.text-info {
    color: #0dcaf0 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-danger {
    color: #dc3545 !important;
}

/* Card headers should remain white text */
.card-header {
    color: white !important;
}

.card-header * {
    color: white !important;
}

/* Ensure all other card content is dark */
.card-body, .card-body * {
    color: #2c3e50 !important;
}

/* Navigation specific */
.nav-link {
    color: #2c3e50 !important;
}

.nav-link.active {
    color: white !important;
}

/* Table specific */
.table, .table * {
    color: #2c3e50 !important;
}

.table-dark, .table-dark * {
    color: white !important;
}

/* Form elements */
.form-control, .form-select, .form-label {
    color: #2c3e50 !important;
}

/* Buttons */
.btn {
    color: inherit !important;
}

/* Badges */
.badge {
    color: white !important;
}

/* Modal content */
.modal-content, .modal-content * {
    color: #2c3e50 !important;
}

.modal-header, .modal-header * {
    color: white !important;
}

/* Chart placeholders */
.chart-container, .chart-container * {
    color: #2c3e50 !important;
}

/* Progress bars */
.progress {
    color: #2c3e50 !important;
}

/* List items */
ul, ol, li {
    color: #2c3e50 !important;
}

/* Any remaining elements */
* {
    color: inherit !important;
}
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Performance Dashboard Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-lg border-0" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="card-body text-white">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="display-6 mb-3 text-white">
                                <i class="fas fa-chart-line me-3"></i>
                                MTDP Performance Analytics Dashboard
                            </h1>
                            <p class="lead mb-0 text-white">
                                Strategic Performance Monitoring • Activity Linkage Analysis • Cascading Performance Indicators
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="row g-2">
                                <div class="col-12">
                                    <div class="bg-white rounded-3 p-3 mb-2 shadow-sm" style="background-color: rgba(255,255,255,0.95) !important;">
                                        <h2 class="mb-1 fw-bold" style="color: #0d6efd !important;"><?= count($plans) ?></h2>
                                        <small class="fw-semibold" style="color: #2c3e50 !important;">Active MTDP Plans</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="bg-white rounded-3 p-2 shadow-sm" style="background-color: rgba(255,255,255,0.95) !important;">
                                        <h4 class="mb-1 fw-bold" style="color: #198754 !important;"><?= array_sum($workplanCounts['mtdp_plans'] ?? []) ?></h4>
                                        <small class="fw-semibold" style="color: #2c3e50 !important;">Linked Activities</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="bg-white rounded-3 p-2 shadow-sm" style="background-color: rgba(255,255,255,0.95) !important;">
                                        <h4 class="mb-1 fw-bold" style="color: #0dcaf0 !important;">$<?= number_format(array_sum(array_column($performanceAnalytics['financial_performance']['mtdp_plans'] ?? [], 'total_cost')), 0) ?></h4>
                                        <small class="fw-semibold" style="color: #2c3e50 !important;">Total Investment</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Tabs -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <ul class="nav nav-pills nav-fill">
                        <li class="nav-item">
                            <a class="nav-link" href="<?= base_url('reports/mtdp') ?>">
                                <i class="fas fa-table me-2"></i>
                                Traditional Report View
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="<?= base_url('reports/mtdp/performance-analytics') ?>">
                                <i class="fas fa-chart-line me-2"></i>
                                Performance Analytics Dashboard
                            </a>
                        </li>
                    </ul>
                    <div class="mt-3 text-center">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Advanced performance analytics with strategic insights and activity linkage analysis
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Metrics Overview -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card performance-card metric-card text-white shadow">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Performance Score</h6>
                            <h2 class="mb-0">
                                <?php
                                $avgPerformance = 0;
                                $totalPlans = count($performanceAnalytics['strategic_performance']['mtdp_plans'] ?? []);
                                if ($totalPlans > 0) {
                                    $totalScore = array_sum(array_column($performanceAnalytics['strategic_performance']['mtdp_plans'], 'performance_score'));
                                    $avgPerformance = round($totalScore / $totalPlans, 1);
                                }
                                echo $avgPerformance;
                                ?>%
                            </h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-pie fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card performance-card activity-card text-white shadow">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Activity Linkage</h6>
                            <h2 class="mb-0"><?= array_sum($workplanCounts['strategies'] ?? []) ?></h2>
                            <small>Linked Activities</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-tasks fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card performance-card financial-card text-white shadow">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Budget Utilization</h6>
                            <h2 class="mb-0">
                                <?php
                                $totalBudget = array_sum(array_column($performanceAnalytics['financial_performance']['mtdp_plans'] ?? [], 'total_cost'));
                                $utilization = $totalBudget > 0 ? 85 : 0; // Placeholder calculation
                                echo $utilization;
                                ?>%
                            </h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card performance-card strategic-card text-white shadow">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Strategic Alignment</h6>
                            <h2 class="mb-0"><?= count($strategies) ?></h2>
                            <small>Active Strategies</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-bullseye fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analysis Controls -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-gradient-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-sliders-h me-2"></i>Performance Analysis Controls
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="<?= base_url('reports/mtdp') ?>" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar-alt me-1 text-primary"></i>Analysis Period From
                            </label>
                            <input type="date" name="date_from" class="form-control" id="date_from" value="<?= esc($dateFrom ?? '') ?>">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar-alt me-1 text-primary"></i>Analysis Period To
                            </label>
                            <input type="date" name="date_to" class="form-control" id="date_to" value="<?= esc($dateTo ?? '') ?>">
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <div class="w-100">
                                <button type="submit" class="btn btn-primary btn-lg me-2">
                                    <i class="fas fa-analytics"></i> Analyze Performance
                                </button>
                                <a href="<?= base_url('reports/mtdp') ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-refresh"></i> Reset
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card shadow-sm">
                <div class="card-header bg-gradient-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-download me-2"></i>Export & Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-success" onclick="exportPerformanceReport()">
                            <i class="fas fa-file-pdf me-1"></i> Export Performance Report
                        </button>
                        <button class="btn btn-info" onclick="exportActivityDetails()">
                            <i class="fas fa-file-excel me-1"></i> Export Activity Details
                        </button>
                        <button class="btn btn-warning" onclick="printDashboard()">
                            <i class="fas fa-print me-1"></i> Print Dashboard
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Strategic Performance Matrix -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-gradient-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-chess-board me-2"></i>Strategic Performance Matrix
                    </h4>
                    <small>Real-time performance tracking across all MTDP hierarchical levels</small>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($plans as $plan): ?>
                            <?php
                            $planId = $plan['id'];
                            $activityCount = $workplanCounts['mtdp_plans'][$planId] ?? 0;
                            $totalCost = $performanceAnalytics['financial_performance']['mtdp_plans'][$planId]['total_cost'] ?? 0;
                            $performanceScore = $performanceAnalytics['strategic_performance']['mtdp_plans'][$planId]['performance_score'] ?? 0;
                            $performanceClass = $performanceScore >= 80 ? 'success' : ($performanceScore >= 60 ? 'warning' : 'danger');
                            ?>
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card h-100 border-<?= $performanceClass ?> shadow-sm">
                                    <div class="card-header bg-<?= $performanceClass ?> text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-flag me-1"></i>
                                            <?= esc($plan['abbrev']) ?>
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <h6 class="card-title"><?= esc($plan['title']) ?></h6>
                                        <div class="row text-center mb-3">
                                            <div class="col-4">
                                                <div class="border rounded p-2">
                                                    <h5 class="mb-0 text-<?= $performanceClass ?>"><?= round($performanceScore, 1) ?>%</h5>
                                                    <small class="text-muted">Performance</small>
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <div class="border rounded p-2">
                                                    <h5 class="mb-0 text-primary"><?= $activityCount ?></h5>
                                                    <small class="text-muted">Activities</small>
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <div class="border rounded p-2">
                                                    <h5 class="mb-0 text-info">$<?= number_format($totalCost, 0) ?></h5>
                                                    <small class="text-muted">Investment</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="progress mb-2" style="height: 8px;">
                                            <div class="progress-bar bg-<?= $performanceClass ?>" style="width: <?= $performanceScore ?>%"></div>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <?= esc($plan['date_from']) ?> - <?= esc($plan['date_to']) ?>
                                            </small>
                                            <?php if ($activityCount > 0): ?>
                                                <button class="btn btn-outline-primary btn-sm" onclick="showPlanDetails(<?= $planId ?>)">
                                                    <i class="fas fa-eye"></i> Details
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Charts Section -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-gradient-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-radar me-2"></i>Performance by Component Type
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="performanceRadarChart" height="300"></canvas>
                    <div class="text-center mt-3" id="radarChartPlaceholder">
                        <div class="d-flex justify-content-center align-items-center" style="height: 200px;">
                            <div class="text-muted">
                                <i class="fas fa-chart-radar fa-3x mb-3 text-primary"></i>
                                <h6>Performance Radar Chart</h6>
                                <p class="small">Visualizing performance across different strategic components</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-gradient-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>Investment Distribution by Performance
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="investmentPieChart" height="300"></canvas>
                    <div class="text-center mt-3" id="pieChartPlaceholder">
                        <div class="d-flex justify-content-center align-items-center" style="height: 200px;">
                            <div class="text-muted">
                                <i class="fas fa-chart-pie fa-3x mb-3 text-success"></i>
                                <h6>Investment Distribution Chart</h6>
                                <p class="small">Showing investment allocation by performance levels</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Linkage Analysis -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-gradient-warning text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-link me-2"></i>Activity Linkage Analysis
                    </h4>
                    <small>Comprehensive view of workplan activities linked to strategic objectives</small>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="activityLinkageTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>Strategic Component</th>
                                    <th>Type</th>
                                    <th>Linked Activities</th>
                                    <th>Total Investment</th>
                                    <th>Avg Performance</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($spas as $spa): ?>
                                    <?php
                                    $spaId = $spa['id'];
                                    $activityCount = $workplanCounts['spas'][$spaId] ?? 0;
                                    $totalCost = $performanceAnalytics['financial_performance']['spas'][$spaId]['total_cost'] ?? 0;
                                    $performanceScore = $performanceAnalytics['strategic_performance']['spas'][$spaId]['performance_score'] ?? 0;
                                    $performanceClass = $performanceScore >= 80 ? 'success' : ($performanceScore >= 60 ? 'warning' : 'danger');
                                    ?>
                                    <tr>
                                        <td>
                                            <strong><?= esc($spa['title']) ?></strong>
                                            <br><small class="text-muted"><?= esc($spa['code']) ?></small>
                                        </td>
                                        <td><span class="badge bg-primary">SPA</span></td>
                                        <td>
                                            <span class="badge bg-info fs-6"><?= $activityCount ?></span>
                                        </td>
                                        <td>$<?= number_format($totalCost, 2) ?></td>
                                        <td>
                                            <?php if ($performanceScore > 0): ?>
                                                <span class="badge bg-<?= $performanceClass ?> fs-6"><?= round($performanceScore, 1) ?>%</span>
                                            <?php else: ?>
                                                <span class="text-muted">N/A</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $spa['spa_status'] == 1 ? 'success' : 'secondary' ?>">
                                                <?= $spa['spa_status'] == 1 ? 'Active' : 'Inactive' ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($activityCount > 0): ?>
                                                <button class="btn btn-sm btn-outline-info" onclick="showLinkedActivities('spa', <?= $spaId ?>)">
                                                    <i class="fas fa-list"></i> View Activities
                                                </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>

                                <?php foreach ($strategies as $strategy): ?>
                                    <?php
                                    $strategyId = $strategy['id'];
                                    $activityCount = $workplanCounts['strategies'][$strategyId] ?? 0;
                                    $totalCost = $performanceAnalytics['financial_performance']['strategies'][$strategyId]['total_cost'] ?? 0;
                                    $performanceScore = $performanceAnalytics['strategic_performance']['strategies'][$strategyId]['performance_score'] ?? 0;
                                    $performanceClass = $performanceScore >= 80 ? 'success' : ($performanceScore >= 60 ? 'warning' : 'danger');
                                    ?>
                                    <tr>
                                        <td>
                                            <strong><?= esc($strategy['strategy']) ?></strong>
                                            <br><small class="text-muted">Strategy Level</small>
                                        </td>
                                        <td><span class="badge bg-success">Strategy</span></td>
                                        <td>
                                            <span class="badge bg-info fs-6"><?= $activityCount ?></span>
                                        </td>
                                        <td>$<?= number_format($totalCost, 2) ?></td>
                                        <td>
                                            <?php if ($performanceScore > 0): ?>
                                                <span class="badge bg-<?= $performanceClass ?> fs-6"><?= round($performanceScore, 1) ?>%</span>
                                            <?php else: ?>
                                                <span class="text-muted">N/A</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $strategy['strategies_status'] == 1 ? 'success' : 'secondary' ?>">
                                                <?= $strategy['strategies_status'] == 1 ? 'Active' : 'Inactive' ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($activityCount > 0): ?>
                                                <button class="btn btn-sm btn-outline-info" onclick="showLinkedActivities('strategy', <?= $strategyId ?>)">
                                                    <i class="fas fa-list"></i> View Activities
                                                </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Trends -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-gradient-dark text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>Performance Trends & Insights
                    </h4>
                    <small>Historical performance analysis and predictive insights</small>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <canvas id="performanceTrendChart" height="200"></canvas>
                            <div class="text-center mt-3" id="trendChartPlaceholder">
                                <div class="d-flex justify-content-center align-items-center" style="height: 150px;">
                                    <div class="text-muted">
                                        <i class="fas fa-chart-line fa-3x mb-3 text-warning"></i>
                                        <h6>Performance Trend Analysis</h6>
                                        <p class="small">Historical performance trends and forecasting</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light h-100">
                                <div class="card-body">
                                    <h6 class="card-title">Key Insights</h6>
                                    <ul class="list-unstyled">
                                        <li class="mb-2">
                                            <i class="fas fa-arrow-up text-success me-2"></i>
                                            <strong><?= count($strategies) ?></strong> strategies are actively linked to workplan activities
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-dollar-sign text-info me-2"></i>
                                            Average investment per activity: <strong>$<?= array_sum($workplanCounts['mtdp_plans'] ?? []) > 0 ? number_format(array_sum(array_column($performanceAnalytics['financial_performance']['mtdp_plans'] ?? [], 'total_cost')) / array_sum($workplanCounts['mtdp_plans'] ?? []), 0) : 0 ?></strong>
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-chart-pie text-warning me-2"></i>
                                            Performance distribution shows <strong><?= count(array_filter($performanceAnalytics['strategic_performance']['mtdp_plans'] ?? [], function($p) { return $p['performance_score'] >= 80; })) ?></strong> high-performing plans
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-link text-primary me-2"></i>
                                            Strategic alignment: <strong><?= round((count($strategies) / max(count($plans), 1)) * 100, 1) ?>%</strong> strategy coverage
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Activity Details Modal -->
<div class="modal fade" id="activityDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-list me-2"></i>Linked Activities Details
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="activityDetailsContent">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading activity details...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="exportActivityData()">
                    <i class="fas fa-download me-1"></i>Export Data
                </button>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#activityLinkageTable').DataTable({
        responsive: true,
        pageLength: 10,
        order: [[2, 'desc']], // Sort by activity count
        columnDefs: [
            { targets: [2, 3, 4], className: 'text-center' }
        ]
    });

    // Hide placeholders when charts are loaded
    document.getElementById('radarChartPlaceholder').style.display = 'none';
    document.getElementById('pieChartPlaceholder').style.display = 'none';
    document.getElementById('trendChartPlaceholder').style.display = 'none';

    // Performance Radar Chart
    const radarCtx = document.getElementById('performanceRadarChart').getContext('2d');
    new Chart(radarCtx, {
        type: 'radar',
        data: {
            labels: ['Strategies', 'KRAs', 'Investments', 'Specific Areas', 'DIPs', 'SPAs'],
            datasets: [{
                label: 'Performance Score (%)',
                data: [
                    <?= isset($chartData['performanceByType']['strategies']) ? $chartData['performanceByType']['strategies'] : 0 ?>,
                    <?= isset($chartData['performanceByType']['kras']) ? $chartData['performanceByType']['kras'] : 0 ?>,
                    <?= isset($chartData['performanceByType']['investments']) ? $chartData['performanceByType']['investments'] : 0 ?>,
                    <?= isset($chartData['performanceByType']['specific_areas']) ? $chartData['performanceByType']['specific_areas'] : 0 ?>,
                    <?= isset($chartData['performanceByType']['dips']) ? $chartData['performanceByType']['dips'] : 0 ?>,
                    <?= isset($chartData['performanceByType']['spas']) ? $chartData['performanceByType']['spas'] : 0 ?>
                ],
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 2,
                pointBackgroundColor: 'rgba(54, 162, 235, 1)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100,
                    ticks: { stepSize: 20 }
                }
            },
            plugins: {
                legend: { display: false }
            }
        }
    });

    // Investment Pie Chart
    const pieCtx = document.getElementById('investmentPieChart').getContext('2d');
    new Chart(pieCtx, {
        type: 'doughnut',
        data: {
            labels: ['Excellent (80-100%)', 'Good (60-79%)', 'Average (40-59%)', 'Poor (0-39%)'],
            datasets: [{
                data: [
                    <?= isset($chartData['costByPerformance']['excellent']) ? $chartData['costByPerformance']['excellent'] : 0 ?>,
                    <?= isset($chartData['costByPerformance']['good']) ? $chartData['costByPerformance']['good'] : 0 ?>,
                    <?= isset($chartData['costByPerformance']['average']) ? $chartData['costByPerformance']['average'] : 0 ?>,
                    <?= isset($chartData['costByPerformance']['poor']) ? $chartData['costByPerformance']['poor'] : 0 ?>
                ],
                backgroundColor: ['#28a745', '#ffc107', '#17a2b8', '#dc3545'],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { position: 'bottom' },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.label + ': $' + context.parsed.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Performance Trend Chart
    const trendCtx = document.getElementById('performanceTrendChart').getContext('2d');
    new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: ['Q1', 'Q2', 'Q3', 'Q4'],
            datasets: [{
                label: 'Overall Performance',
                data: [65, 72, 78, 85], // Placeholder data
                borderColor: 'rgba(75, 192, 192, 1)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: { callback: function(value) { return value + '%'; } }
                }
            }
        }
    });
});

// Function to show linked activities
function showLinkedActivities(componentType, componentId) {
    $('#activityDetailsModal').modal('show');

    $.ajax({
        url: '<?= base_url('reports/mtdp/linked-activities') ?>',
        method: 'GET',
        data: {
            component_type: componentType,
            component_id: componentId,
            date_from: $('#date_from').val(),
            date_to: $('#date_to').val()
        },
        success: function(response) {
            if (response.success && response.data.length > 0) {
                let content = `
                    <div class="mb-3">
                        <div class="alert alert-info">
                            <strong>Total Activities: ${response.total}</strong> linked to this ${componentType.toUpperCase()}
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Activity</th>
                                    <th>Type</th>
                                    <th>Workplan</th>
                                    <th>Branch</th>
                                    <th>Target</th>
                                    <th>Cost</th>
                                    <th>Rating</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                response.data.forEach(activity => {
                    const rating = activity.rating > 0 ? activity.rating.toFixed(1) + '/5' : 'N/A';
                    const cost = activity.total_cost > 0 ? '$' + activity.total_cost.toLocaleString() : '$0';
                    const target = activity.total_target > 0 ? activity.total_target.toLocaleString() : 'N/A';

                    content += `
                        <tr>
                            <td>
                                <strong>${activity.title || 'N/A'}</strong>
                                ${activity.description ? '<br><small class="text-muted">' + activity.description.substring(0, 100) + '...</small>' : ''}
                            </td>
                            <td><span class="badge bg-secondary">${activity.type || 'N/A'}</span></td>
                            <td>${activity.workplan || 'N/A'}</td>
                            <td>${activity.branch || 'N/A'}</td>
                            <td>${target}</td>
                            <td>${cost}</td>
                            <td>${rating !== 'N/A' ? '<span class="badge bg-warning">' + rating + '</span>' : '<span class="text-muted">N/A</span>'}</td>
                            <td>${activity.status ? '<span class="badge bg-info">' + activity.status + '</span>' : '<span class="text-muted">N/A</span>'}</td>
                        </tr>
                    `;
                });

                content += '</tbody></table></div>';
                $('#activityDetailsContent').html(content);
            } else {
                $('#activityDetailsContent').html('<div class="alert alert-warning text-center">No activities found for this component.</div>');
            }
        },
        error: function() {
            $('#activityDetailsContent').html('<div class="alert alert-danger text-center">Error loading activity data. Please try again.</div>');
        }
    });
}

// Export functions
function exportPerformanceReport() {
    window.print();
}

function exportActivityDetails() {
    // Placeholder for export functionality
    alert('Export functionality will be implemented');
}

function exportActivityData() {
    // Placeholder for export functionality
    alert('Export functionality will be implemented');
}

function showPlanDetails(planId) {
    showLinkedActivities('mtdp', planId);
}

function printDashboard() {
    window.print();
}
</script>
<?= $this->endSection() ?>
